#include "BackgroundTask.h"
#include <iostream>

void BackgroundTask::operator()(vector<int> &b)
{
    std::cout << "BackgroundTask is running with vector of size: " << arr.size() << std::endl;
    for (int i = 0; i < arr.size(); i++)
    {
        arr[i] = arr[i] + b[i];
        b[i] = arr[i];
    }
    cout << endl;
}

void BackgroundTask::do_lengthy_work(int n)
{
	cout << "\nDoing lengthy work for " << n << " iterations." << endl;
    for (int i = 0; i < n; i++)
    {
        cout << i << " ";
    }
}