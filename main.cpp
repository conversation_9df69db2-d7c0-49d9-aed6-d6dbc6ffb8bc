#include <iostream>
#include <vector>
#include <thread>
#include <functional>
#include <memory>
#include <list>
#include <mutex>
#include "BackgroundTask.h"

using namespace std;

// 互斥量
list<int> someList;
mutex someListMutex; // 声明一个互斥锁

vector<int>& f(vector<int>& vec)
{
	BackgroundTask task(vec);
	vector<int> a{ 2, 4, 6, 8, 10 };
	// thread(函数,这里传递)的参数是拷贝方式，即使函数定义的是引用
	// 所以要ref包裹才能传递引用。
	thread t(task, ref(a));
	// 线程守护，保证主函数推出前，等待线程返回。
	ThreadGuard g(t);
	return vec;
}

void listPushSomeThing(list<int>& addList)
{
	// 声明一个叫guard的锁守护（智能结束释放），来锁叫someListMutex的互斥锁
	lock_guard guard(someListMutex);
	// 可拆分成下面写法：
	lock(someListMutex);
	
	// 锁下面代码，只有一个线程能运行下面代码。
	someList.insert(someList.end(), addList.begin(), addList.end());
}

int main()
{
	vector<int> vec = { 1, 2, 4, 5, 6 };
	f(vec);
	cout << "\n拷贝传值示例：\n";
	for (int i = 0; i < vec.size(); i++)
	{
		cout << vec[i] << " ";
	}
	cout << endl;
	for (auto b : vec)
	{
		cout << b << " ";
	}

	cout << "\n移动传值示例：\n";
	// 使用 make_unique 创建动态分配的对象
	unique_ptr<BackgroundTask> p = make_unique<BackgroundTask>(vec);
	// unique_ptr 只能用move,不能拷贝
	thread t(&BackgroundTask::do_lengthy_work, move(p), 10);
	t.join(); // 等待线程返回。

	// 测试锁，如果不锁，会出现数据错乱。
	list<int> some_1(10000, 1);
	list<int> some_2(10000, 9);
	thread doList_1(listPushSomeThing, ref(some_1));
	thread doList_2(listPushSomeThing, ref(some_2));
	doList_1.join();
	doList_2.join();
	cout << "\nsomeList:\n";
	for (auto i : someList)
	{
		cout << i << " ";
	}
}